[20-Aug-2025 19:09:21 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getOrderTotals() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php:340
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php(970): Theme25\Backend\Controller\Sale\Order\Edit->prepareOrderTotals(50199)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php(773): Theme25\Backend\Controller\Sale\Order\Edit->recalculateOrderTotals(50199)
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order.php(265): Theme25\Backend\Controller\Sale\Order\Edit->applyCoupon()
#3 [internal function]: Theme25\Backend\Controller\Sale\Order->applyCoupon()
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(287): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerSaleO...', 'applyCoupon', Array)
#6 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor- in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php on line 340
[21-Aug-2025 08:48:57 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function disableVoucher() on null in /home/<USER>/storage_theme25/theme/Backend/Model/Sale/Order.php:255
Stack trace:
#0 [internal function]: Theme25\Backend\Model\Sale\Order->editOrder(50199, Array)
#1 /home/<USER>/storage_theme25/theme/ModelProcessor.php(451): call_user_func_array(Array, Array)
#2 /home/<USER>/storage_theme25/theme/ModelProcessor.php(319): Theme25\ModelProcessor->callModelMethod(Object(Registry), Array, 'sale/order/edit...', Array)
#3 /home/<USER>/theme25/system/engine/proxy.php(25): Theme25\ModelProcessor->Theme25\{closure}(Array, Array)
#4 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php(84): Proxy->__call('editOrder', Array)
#5 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order.php(110): Theme25\Backend\Controller\Sale\Order\Edit->processOrderUpdate(50199)
#6 [internal function]: Theme25\Backend\Controller\Sale\Order->edit()
#7 /home/<USER>/storage_theme25/theme/RequestProcessor.php(287): call_user_func_array(Array, Array)
 in /home/<USER>/storage_theme25/theme/Backend/Model/Sale/Order.php on line 255
[21-Aug-2025 11:34:08 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getOrder() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php:926
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php(788): Theme25\Backend\Controller\Sale\Order\Edit->recalculateOrderTotals(50199)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order.php(265): Theme25\Backend\Controller\Sale\Order\Edit->applyCoupon()
#2 [internal function]: Theme25\Backend\Controller\Sale\Order->applyCoupon()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(287): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerSaleO...', 'applyCoupon', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerSaleO...', 'applyCoupon', Array)
#6 /home/<USER>/storage_theme25/modification/system/engine/action.php(94): RequestProcessor->process(Objec in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php on line 926
[21-Aug-2025 11:41:08 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getOrder() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php:926
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php(788): Theme25\Backend\Controller\Sale\Order\Edit->recalculateOrderTotals(50199)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order.php(265): Theme25\Backend\Controller\Sale\Order\Edit->applyCoupon()
#2 [internal function]: Theme25\Backend\Controller\Sale\Order->applyCoupon()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(287): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerSaleO...', 'applyCoupon', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerSaleO...', 'applyCoupon', Array)
#6 /home/<USER>/storage_theme25/modification/system/engine/action.php(94): RequestProcessor->process(Objec in /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order/Edit.php on line 926
