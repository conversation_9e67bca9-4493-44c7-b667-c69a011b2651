<?php

namespace Theme25\Backend\Model\Sale;


class Order extends \Theme25\Model {

    private $sqlHelper;

    private $fieldRules = [
        'order' => [
            'invoice_prefix'     => 'string',
            'store_id'           => 'int',
            'store_name'         => 'string',
            'store_url'          => 'string',
            'customer_id'        => 'int',
            'customer_group_id'  => 'int',
            'firstname'          => 'string',
            'lastname'           => 'string',
            'email'              => 'string',
            'telephone'          => 'string',
            'custom_field'       => 'json',
            'payment_firstname'  => 'string',
            'payment_lastname'   => 'string',
            'payment_company'    => 'string',
            'payment_address_1'  => 'string',
            'payment_address_2'  => 'string',
            'payment_city'       => 'string',
            'payment_postcode'   => 'string',
            'payment_country'    => 'string',
            'payment_country_id' => 'int',
            'payment_zone'       => 'string',
            'payment_zone_id'    => 'int',
            'payment_address_format' => 'string',
            'payment_custom_field'   => 'json',
            'payment_method'     => 'string',
            'payment_code'       => 'string',
            'shipping_firstname' => 'string',
            'shipping_lastname'  => 'string',
            'shipping_company'   => 'string',
            'shipping_address_1' => 'string',
            'shipping_address_2' => 'string',
            'shipping_city'      => 'string',
            'shipping_postcode'  => 'string',
            'shipping_country'   => 'string',
            'shipping_country_id'=> 'int',
            'shipping_zone'      => 'string',
            'shipping_zone_id'   => 'int',
            'shipping_address_format' => 'string',
            'shipping_custom_field'   => 'json',
            'shipping_method'    => 'string',
            'shipping_code'      => 'string',
            'comment'            => 'string',
            'total'              => 'float',
            'affiliate_id'       => 'int',
            'commission'         => 'float',
            'marketing_id'       => 'int',
            'tracking'           => 'string',
            'language_id'        => 'int',
            'currency_id'        => 'int',
            'currency_code'      => 'string',
            'currency_value'     => 'float',
            'ip'                 => 'string',
            'forwarded_ip'       => 'string',
            'user_agent'         => 'string',
            'accept_language'    => 'string',
            'date_added'         => 'now',
            'date_modified'      => 'now'
        ],
    
        'order_product' => [
            'order_id'   => 'int',
            'product_id' => 'int',
            'name'       => 'string',
            'model'      => 'string',
            'quantity'   => 'int',
            'price'      => 'float',
            'total'      => 'float',
            'tax'        => 'float',
            'reward'     => 'int'
        ],
    
        'order_option' => [
            'order_id'              => 'int',
            'order_product_id'      => 'int',
            'product_option_id'     => 'int',
            'product_option_value_id'=> 'int',
            'name'                  => 'string',
            'value'                 => 'string',
            'type'                  => 'string'
        ],
    
        'order_voucher' => [
            'order_id'        => 'int',
            'description'     => 'string',
            'code'            => 'string',
            'from_name'       => 'string',
            'from_email'      => 'string',
            'to_name'         => 'string',
            'to_email'        => 'string',
            'voucher_theme_id'=> 'int',
            'message'         => 'string',
            'amount'          => 'float',
            'voucher_id'      => 'int'
        ],
    
        'order_total' => [
            'order_id'   => 'int',
            'code'       => 'string',
            'title'      => 'string',
            'text'       => 'string',
            'value'      => 'float',
            'sort_order' => 'int'
        ]
    ];

    public function __construct($registry) {
        parent::__construct($registry, true); // true означава, че се зарежда и стандартния модел
        $this->sqlHelper = \Theme25\Helper\SQLHelper::getInstance($registry);
    }

    public function getFieldsRules() {
        return $this->fieldRules;
    }

    public function addOrder($data) {

        $this->sqlHelper->addFieldRules($this->fieldRules);

        // Поръчка
        $fields = $this->sqlHelper->prepareSQLfields($data, 'order');
        $this->dbQuery("INSERT INTO `" . DB_PREFIX . "order` SET " . $this->sqlHelper->buildSQLfields($fields));

        $order_id = $this->db->getLastId();
    
        // Продукти
        if (!empty($data['products'])) {
            foreach ($data['products'] as $product) {
                $product['order_id'] = $order_id;
                $fields = $this->sqlHelper->prepareSQLfields($product, 'order_product');
                
                $this->dbQuery("INSERT INTO " . DB_PREFIX . "order_product SET " . 
                $this->sqlHelper->buildSQLfields($fields));
                
                $order_product_id = $this->db->getLastId();
                
                // Опции на продукта
                if (!empty($product['option'])) {
                    foreach ($product['option'] as $option) {
                        $option['order_id'] = $order_id;
                        $option['order_product_id'] = $order_product_id;
                        $fields = $this->sqlHelper->prepareSQLfields($option, 'order_option');
                        $this->dbQuery("INSERT INTO " . DB_PREFIX . "order_option SET " . 
                        $this->sqlHelper->buildSQLfields($fields));
                    }
                }

            }
        }
    
        // Ваучери
        $this->loadModelAs('extension/total/voucher', 'modelVoucher');
        if (!empty($data['vouchers'])) {
            foreach ($data['vouchers'] as $voucher) {
                $voucher['order_id'] = $order_id;
                $fields = $this->sqlHelper->prepareSQLfields($voucher, 'order_voucher');
                $this->dbQuery("INSERT INTO " . DB_PREFIX . "order_voucher SET " . 
                $this->sqlHelper->buildSQLfields($fields));
    
                $order_voucher_id = $this->db->getLastId();
                $voucher_id = $this->modelVoucher->addVoucher($order_id, $voucher);
    
                $this->dbQuery("
                    UPDATE " . DB_PREFIX . "order_voucher
                    SET voucher_id = '" . (int)$voucher_id . "'
                    WHERE order_voucher_id = '" . (int)$order_voucher_id . "'
                ");
            }
        }
    
        // Тотали
        if (!empty($data['totals'])) {
            foreach ($data['totals'] as $total) {
                $total['order_id'] = $order_id;
                $fields = $this->sqlHelper->prepareSQLfields($total, 'order_total');
                $this->dbQuery("INSERT INTO " . DB_PREFIX . "order_total SET " . 
                $this->sqlHelper->buildSQLfields($fields));
            }
        }
    
        return $order_id;
    }
    

	public function editOrder($order_id, $data) {

        if(!$order_id) {
            return false;
        }

        // Void the order first
        $this->addOrderHistory($order_id, 0);

        $this->sqlHelper->addFieldRules($this->fieldRules);
    
        // Полета за UPDATE на order
        $fields = $this->sqlHelper->prepareSQLfields($data, 'order');

        $sql = "UPDATE `" . DB_PREFIX . "order` SET " . $this->sqlHelper->buildSQLfields($fields) . " WHERE order_id = '" . (int)$order_id . "'";

        $this->dbQuery($sql);
    
        // Изчистваме продуктите и опциите
        $this->sqlHelper->executeDeleteFrom([
            'bulk' => [
                [ 'table' => 'order_product', 'where' => "order_id = '" . (int)$order_id . "'" ],
                [ 'table' => 'order_option', 'where' => "order_id = '" . (int)$order_id . "'" ]
            ]
        ]);

        // Продукти
        if (!empty($data['products'])) {
            foreach ($data['products'] as $product) {
                $product['order_id'] = $order_id;
                $fields = $this->sqlHelper->prepareSQLfields($product, 'order_product');
                $sql = "INSERT INTO " . DB_PREFIX . "order_product SET " . $this->sqlHelper->buildSQLfields($fields);
               $this->dbQuery($sql);
    
                $order_product_id = $this->db->getLastId();
    
                if (!empty($product['option'])) {
                    foreach ($product['option'] as $option) {
                        $option['order_id'] = $order_id;
                        $option['order_product_id'] = $order_product_id;
                        $fields = $this->sqlHelper->prepareSQLfields($option, 'order_option');
                        $sql = "INSERT INTO " . DB_PREFIX . "order_option SET " . $this->sqlHelper->buildSQLfields($fields);
                        $this->dbQuery($sql);
                    }
                }
            }
        }
    
        // Gift Voucher
        $this->loadModelAs('extension/total/voucher', 'modelVoucher');
        $this->modelVoucher->disableVoucher($order_id);
    
        // Ваучери
        $this->sqlHelper->executeDeleteFrom([ 'table' => 'order_voucher', 'where' => "order_id = '" . (int)$order_id . "'" ]);
    
        if (!empty($data['vouchers'])) {
            foreach ($data['vouchers'] as $voucher) {
                $voucher['order_id'] = $order_id;
                $fields = $this->sqlHelper->prepareSQLfields($voucher, 'order_voucher');
                $sql = "INSERT INTO " . DB_PREFIX . "order_voucher SET " . $this->sqlHelper->buildSQLfields($fields);
                $this->dbQuery($sql);
    
                $order_voucher_id = $this->db->getLastId();
                $voucher_id = $this->modelVoucher->addVoucher($order_id, $voucher);
    
                $this->dbQuery("
                    UPDATE " . DB_PREFIX . "order_voucher
                    SET voucher_id = '" . (int)$voucher_id . "'
                    WHERE order_voucher_id = '" . (int)$order_voucher_id . "'
                ");
            }
        }
    
        // Totals
        $this->sqlHelper->executeDeleteFrom([ 'table' => 'order_total', 'where' => "order_id = '" . (int)$order_id . "'" ]);
    
        if (!empty($data['totals'])) {
            foreach ($data['totals'] as $total) {
                $total['order_id'] = $order_id;
                $fields = $this->sqlHelper->prepareSQLfields($total, 'order_total');
                $sql = "INSERT INTO " . DB_PREFIX . "order_total SET " . $this->sqlHelper->buildSQLfields($fields);
                $this->dbQuery($sql);
            }
        }
    }
    

	public function deleteOrder($order_id) {

        if(!$order_id) {
            return false;
        }

		// Void the order first
		$this->addOrderHistory($order_id, 0);

        $where = "order_id = '" . (int)$order_id . "'";

        $this->sqlHelper->executeDeleteFrom([
            'bulk' => [
                [ 'table' => 'order', 'where' => $where ],
                [ 'table' => 'order_product', 'where' => $where ],
                [ 'table' => 'order_option', 'where' => $where ],
                [ 'table' => 'order_voucher', 'where' => $where ],
                [ 'table' => 'order_total', 'where' => $where ],
                [ 'table' => 'order_history', 'where' => $where ],
                [ 'tables' => ['order_recurring', 'order_recurring_transaction'], 'alias' => ['or', 'ort'], 'where' => $where . " AND ort.order_recurring_id = `or`.order_recurring_id" ],
                [ 'table' => 'customer_transaction', 'where' => $where ]
            ]
        ]);

		// Gift Voucher
		$this->loadModelAs('extension/total/voucher', 'modelVoucher');

		$this->modelVoucher->disableVoucher($order_id);
	}


    /**
     * Получаване на всички поръчки за даден клиент
     * Разширяваме функционалността на родителския клас
     * 
     * @param int $customer_id ID на клиента
     * @return array Масив с поръчки
     */
    public function getOrdersByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.customer_id,
                o.customer_group_id,
                o.firstname,
                o.lastname,
                o.email,
                o.telephone,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.currency_value,
                o.date_added,
                o.date_modified,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.date_added DESC
        ");

        return $query->rows;
    }

    /**
     * Получаване на броя поръчки за даден клиент
     * 
     * @param int $customer_id ID на клиента
     * @return int Брой поръчки
     */
    public function getTotalOrdersByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT COUNT(*) as total 
            FROM `" . DB_PREFIX . "order` 
            WHERE customer_id = '" . (int)$customer_id . "'
        ");

        return (int)$query->row['total'];
    }

    /**
     * Получаване на активни поръчки за даден клиент
     * (поръчки, които не са завършени или отказани)
     * 
     * @param int $customer_id ID на клиента
     * @return array Масив с активни поръчки
     */
    public function getActiveOrdersByCustomerId($customer_id) {
        // Статуси, които се считат за активни (не завършени/отказани)
        $active_statuses = [1, 2, 3, 15]; // Pending, Processing, Shipped, Processing
        
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.customer_id,
                o.firstname,
                o.lastname,
                o.email,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.date_added,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND o.order_status_id IN (" . implode(',', array_map('intval', $active_statuses)) . ")
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.date_added DESC
        ");

        return $query->rows;
    }

    /**
     * Получаване на общата сума на поръчките за даден клиент
     * 
     * @param int $customer_id ID на клиента
     * @param array $status_ids Масив със статуси на поръчки (по подразбиране всички)
     * @return float Обща сума
     */
    public function getTotalOrderValueByCustomerId($customer_id, $status_ids = []) {
        $sql = "
            SELECT SUM(total) as total_value 
            FROM `" . DB_PREFIX . "order` 
            WHERE customer_id = '" . (int)$customer_id . "'
        ";
        
        if (!empty($status_ids)) {
            $sql .= " AND order_status_id IN (" . implode(',', array_map('intval', $status_ids)) . ")";
        }

        $query = $this->db->query($sql);

        return (float)$query->row['total_value'];
    }

    /**
     * Получаване на последната поръчка за даден клиент
     * 
     * @param int $customer_id ID на клиента
     * @return array|null Данни за последната поръчка или null
     */
    public function getLastOrderByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.customer_id,
                o.firstname,
                o.lastname,
                o.email,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.date_added,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY o.date_added DESC
            LIMIT 1
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * Проверява дали клиент има поръчки
     * 
     * @param int $customer_id ID на клиента
     * @return bool True ако има поръчки, false ако няма
     */
    public function hasOrdersByCustomerId($customer_id) {
        $query = $this->db->query("
            SELECT COUNT(*) as total 
            FROM `" . DB_PREFIX . "order` 
            WHERE customer_id = '" . (int)$customer_id . "'
            LIMIT 1
        ");

        return (int)$query->row['total'] > 0;
    }

    /**
     * Получаване на статистики за поръчките на клиент
     * 
     * @param int $customer_id ID на клиента
     * @return array Статистики
     */
    public function getOrderStatsByCustomerId($customer_id) {
        $total_orders = $this->getTotalOrdersByCustomerId($customer_id);
        $total_value = $this->getTotalOrderValueByCustomerId($customer_id);
        $active_orders = count($this->getActiveOrdersByCustomerId($customer_id));
        $last_order = $this->getLastOrderByCustomerId($customer_id);

        return [
            'total_orders' => $total_orders,
            'total_value' => $total_value,
            'active_orders' => $active_orders,
            'last_order_date' => $last_order ? $last_order['date_added'] : null,
            'last_order_id' => $last_order ? $last_order['order_id'] : null,
            'average_order_value' => $total_orders > 0 ? ($total_value / $total_orders) : 0
        ];
    }

    /**
     * Получаване на поръчки за даден клиент с пагинация
     * 
     * @param int $customer_id ID на клиента
     * @param array $data Параметри за филтриране и пагинация
     * @return array Масив с поръчки
     */
    public function getOrdersByCustomerIdPaginated($customer_id, $data = []) {
        $sql = "
            SELECT 
                o.order_id,
                o.customer_id,
                o.firstname,
                o.lastname,
                o.email,
                o.telephone,
                o.order_status_id,
                o.total,
                o.currency_code,
                o.currency_value,
                o.date_added,
                o.date_modified,
                os.name as order_status
            FROM `" . DB_PREFIX . "order` o
            LEFT JOIN `" . DB_PREFIX . "order_status` os ON (o.order_status_id = os.order_status_id)
            WHERE o.customer_id = '" . (int)$customer_id . "'
            AND os.language_id = '" . (int)$this->config->get('config_language_id') . "'
        ";

        // Филтриране по статус
        if (isset($data['filter_order_status_id']) && $data['filter_order_status_id'] !== '') {
            $sql .= " AND o.order_status_id = '" . (int)$data['filter_order_status_id'] . "'";
        }

        // Сортиране
        $sort_data = [
            'o.order_id',
            'o.firstname',
            'o.lastname',
            'o.email',
            'o.total',
            'o.date_added',
            'order_status'
        ];

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY o.date_added";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        // Пагинация
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    public function addOrderHistory($order_id, $order_status_id, $comment = '', $notify = false, $override = false) {
		$order_info = $this->getOrder($order_id);
		
		if ($order_info) {
			// Fraud Detection
			$this->loadModelAs('customer/customer', 'customerModel');

			$customer_info = $this->customerModel->getCustomer($order_info['customer_id']);

			if ($customer_info && $customer_info['safe']) {
				$safe = true;
			} else {
				$safe = false;
			}

			// Only do the fraud check if the customer is not on the safe list and the order status is changing into the complete or process order status
			if (!$safe && !$override && in_array($order_status_id, array_merge($this->config->get('config_processing_status'), $this->config->get('config_complete_status')))) {
				// Anti-Fraud
				$this->loadModelAs('setting/extension', 'extensionModel');

				$extensions = $this->extensionModel->getExtensions('fraud');

				foreach ($extensions as $extension) {
					if ($this->config->get('fraud_' . $extension['code'] . '_status')) {
						$this->loadModelAs('extension/fraud/' . $extension['code'], 'fraudModel_' . $extension['code']);

						if (property_exists($this->{'fraudModel_' . $extension['code']}, 'check')) {
							$fraud_status_id = $this->{'fraudModel_' . $extension['code']}->check($order_info);
	
							if ($fraud_status_id) {
								$order_status_id = $fraud_status_id;
							}
						}
					}
				}
			}

			// If current order status is not processing or complete but new status is processing or complete then commence completing the order
			if (!in_array($order_info['order_status_id'], array_merge($this->config->get('config_processing_status'), $this->config->get('config_complete_status'))) && in_array($order_status_id, array_merge($this->config->get('config_processing_status'), $this->config->get('config_complete_status')))) {
				// Redeem coupon, vouchers and reward points
				$order_totals = $this->getOrderTotals($order_id);

				foreach ($order_totals as $order_total) {
					$this->loadModelAs('extension/total/' . $order_total['code'], 'modelTotal_' . $order_total['code']);

					if (property_exists($this->{'modelTotal_' . $order_total['code']}, 'confirm')) {
						// Confirm coupon, vouchers and reward points
						$fraud_status_id = $this->{'modelTotal_' . $order_total['code']}->confirm($order_info, $order_total);
						
						// If the balance on the coupon, vouchers and reward points is not enough to cover the transaction or has already been used then the fraud order status is returned.
						if ($fraud_status_id) {
							$order_status_id = $fraud_status_id;
						}
					}
				}

				// Stock subtraction
				$order_products = $this->getOrderProducts($order_id);

				foreach ($order_products as $order_product) {
					$this->db->query("UPDATE " . DB_PREFIX . "product SET quantity = (quantity - " . (int)$order_product['quantity'] . ") WHERE product_id = '" . (int)$order_product['product_id'] . "' AND subtract = '1'");

					$order_options = $this->getOrderOptions($order_id, $order_product['order_product_id']);

					foreach ($order_options as $order_option) {
						$this->db->query("UPDATE " . DB_PREFIX . "product_option_value SET quantity = (quantity - " . (int)$order_product['quantity'] . ") WHERE product_option_value_id = '" . (int)$order_option['product_option_value_id'] . "' AND subtract = '1'");
					}
				}
				
				// Add commission if sale is linked to affiliate referral.
				if ($order_info['affiliate_id'] && $this->config->get('config_affiliate_auto')) {
					$this->load->model('account/customer');

					if (!$this->model_account_customer->getTotalTransactionsByOrderId($order_id)) {
						$this->model_account_customer->addTransaction($order_info['affiliate_id'], $this->language->get('text_order_id') . ' #' . $order_id, $order_info['commission'], $order_id);
					}
				}
			}

			// Update the DB with the new statuses
			$this->db->query("UPDATE `" . DB_PREFIX . "order` SET order_status_id = '" . (int)$order_status_id . "', date_modified = NOW() WHERE order_id = '" . (int)$order_id . "'");

			$this->db->query("INSERT INTO " . DB_PREFIX . "order_history SET order_id = '" . (int)$order_id . "', order_status_id = '" . (int)$order_status_id . "', notify = '" . (int)$notify . "', comment = '" . $this->db->escape($comment) . "', date_added = NOW()");

			// If old order status is the processing or complete status but new status is not then commence restock, and remove coupon, voucher and reward history
			if (in_array($order_info['order_status_id'], array_merge($this->config->get('config_processing_status'), $this->config->get('config_complete_status'))) && !in_array($order_status_id, array_merge($this->config->get('config_processing_status'), $this->config->get('config_complete_status')))) {
				// Restock
				$order_products = $this->getOrderProducts($order_id);

				foreach($order_products as $order_product) {
					$this->db->query("UPDATE `" . DB_PREFIX . "product` SET quantity = (quantity + " . (int)$order_product['quantity'] . ") WHERE product_id = '" . (int)$order_product['product_id'] . "' AND subtract = '1'");

					$order_options = $this->getOrderOptions($order_id, $order_product['order_product_id']);

					foreach ($order_options as $order_option) {
						$this->db->query("UPDATE " . DB_PREFIX . "product_option_value SET quantity = (quantity + " . (int)$order_product['quantity'] . ") WHERE product_option_value_id = '" . (int)$order_option['product_option_value_id'] . "' AND subtract = '1'");
					}
				}

				// Remove coupon, vouchers and reward points history
				$order_totals = $this->getOrderTotals($order_id);
				
				foreach ($order_totals as $order_total) {
					$this->loadModelAs('extension/total/' . $order_total['code'], 'modelTotal_' . $order_total['code']);

					if (property_exists($this->{'modelTotal_' . $order_total['code']}, 'unconfirm')) {
						$this->{'modelTotal_' . $order_total['code']}->unconfirm($order_id);
					}
				}

				// Remove commission if sale is linked to affiliate referral.
				if ($order_info['affiliate_id']) {
					$this->loadModelAs('account/customer', 'customerModel');
					
					$this->customerModel->deleteTransactionByOrderId($order_id);
				}
			}

			$this->cache->delete('product');
		}
	}

    public function getOrderProducts($order_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "order_product WHERE order_id = '" . (int)$order_id . "'");
		
		return $query->rows;
	}
	
	public function getOrderOptions($order_id, $order_product_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "order_option WHERE order_id = '" . (int)$order_id . "' AND order_product_id = '" . (int)$order_product_id . "'");
		
		return $query->rows;
	}
	
	public function getOrderVouchers($order_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "order_voucher WHERE order_id = '" . (int)$order_id . "'");
	
		return $query->rows;
	}
	
	public function getOrderTotals($order_id) {
		$query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "order_total` WHERE order_id = '" . (int)$order_id . "' ORDER BY sort_order ASC");
		
		return $query->rows;
	}	
    
}
