<?php

namespace Theme25\Model\Extension\Total;

class SubTotal extends \Theme25\Model {
	public function getTotal($total) {
		$this->loadLanguage('extension/total/sub_total');

		$sub_total = $this->cart->getSubTotal();

		F()->log->developer($sub_total, __FILE__, __LINE__);

		if (!empty($this->session->data['vouchers'])) {
			foreach ($this->session->data['vouchers'] as $voucher) {
				$sub_total += $voucher['amount'];
			}
		}

		$total['totals'][] = array(
			'code'       => 'sub_total',
			'title'      => $this->getLanguageText('text_sub_total'),
			'value'      => $sub_total,
			'sort_order' => $this->getConfig('sub_total_sort_order')
		);

		$total['total'] += $sub_total;

		F()->log->developer($total, __FILE__, __LINE__);
	}
}
